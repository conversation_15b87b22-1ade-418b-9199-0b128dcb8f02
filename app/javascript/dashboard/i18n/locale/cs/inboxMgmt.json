{"INBOX_MGMT": {"HEADER": "<PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "K tomuto účtu nejsou připojeny žádné doru<PERSON>."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "BODY": "Vyberte si poskytovatele, který chcete integrovat do Chatwoot."}, "INBOX": {"TITLE": "Vytvořit doručenou poštu", "BODY": "Ověřte si svůj účet a vytvořte si doručenou poštu."}, "AGENT": {"TITLE": "Přidat agenty", "BODY": "Přidat agenty do vytvořené schránky."}, "FINISH": {"TITLE": "Voilà!", "BODY": "V<PERSON>e je nastaveno!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter your inbox name (eg: Acme Inc)", "ERROR": "Please enter a valid inbox name"}, "WEBSITE_NAME": {"LABEL": "Website Name", "PLACEHOLDER": "Enter your website name (eg: Acme Inc)"}, "FB": {"HELP": "PS: Přihlášením získáme přístup pouze ke zprávám va<PERSON><PERSON> str<PERSON>. Vaše soukromé zprávy nikdy nemo<PERSON> být přístupné <PERSON>.", "CHOOSE_PAGE": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "CHOOSE_PLACEHOLDER": "Vyberte stránku ze seznamu", "INBOX_NAME": "<PERSON><PERSON><PERSON><PERSON>", "ADD_NAME": "Zadejte ná<PERSON>v <PERSON>", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "<PERSON><PERSON><PERSON><PERSON> hodnotu", "CREATE_INBOX": "Vytvořit doručenou poštu"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Pokračovat pomocí Instagramu", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Připojte svůj Instagram profil", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "Chcete-li přidat svůj Twitter profil jako ka<PERSON>, musíte ověřit svůj Twitter profil kliknutím na tlačítko 'Přihlásit se přes Twitter' ", "ERROR_MESSAGE": "There was an error connecting to Twitter, please try again", "TWEETS": {"ENABLE": "Create conversations from mentioned Tweets"}}, "WEBSITE_CHANNEL": {"TITLE": "<PERSON><PERSON><PERSON>", "DESC": "Vytvořte si kanál pro vaše webové stránky a začněte podporovat své zákazníky prostřednictvím našeho widgetu.", "LOADING_MESSAGE": "Vytváření podpůrného kanálu webových stránek", "CHANNEL_AVATAR": {"LABEL": "Avatar ka<PERSON>"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "URL webového háčku", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "Zadejte prosím platnou URL"}, "CHANNEL_DOMAIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> do<PERSON> webu (např. acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "Úvod uvítání", "PLACEHOLDER": "Ahoj!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Vítejte Tagline", "PLACEHOLDER": "Snadno se s námi spojujeme. Požádejte nás o cokoliv, nebo sdílejte svou zpětnou vazbu."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Acme Inc obvykle odpovídá do několika hodin."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Povolit uvítání", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "REPLY_TIME": {"TITLE": "Nastavit čas odpovědi", "IN_A_FEW_MINUTES": "Do několika minut", "IN_A_FEW_HOURS": "Do několika hodin", "IN_A_DAY": "Do dne", "HELP_TEXT": "Tento čas odpovědi bude zobrazen na widgetu"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON> widgetu", "PLACEHOLDER": "Aktualizovat barvu widgetu použitou ve widgetu"}, "SUBMIT_BUTTON": "Vytvořit doručenou poštu", "API": {"ERROR_MESSAGE": "We were not able to create a website channel, please try again"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Channel", "DESC": "Integrate Twilio and start supporting your customers via SMS or WhatsApp.", "ACCOUNT_SID": {"LABEL": "SID účtu", "PLACEHOLDER": "Zadejte SID vašeho Twilio účtu", "ERROR": "Toto pole je povinné"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "Toto pole je povinné"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "Toto pole je povinné"}, "MESSAGING_SERVICE_SID": {"LABEL": "Messaging Service SID", "PLACEHOLDER": "Please enter your Twilio Messaging Service SID", "ERROR": "Toto pole je povinné", "USE_MESSAGING_SERVICE": "Use a Twilio Messaging Service"}, "CHANNEL_TYPE": {"LABEL": "Channel Type", "ERROR": "Please select your Channel Type"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Zadejte svůj ověřovací Token Twilio", "ERROR": "Toto pole je povinné"}, "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "Toto pole je povinné"}, "PHONE_NUMBER": {"LABEL": "Telefonní číslo", "PLACEHOLDER": "Zadejte prosím telefonní číslo, ze kterého bude zpráva odeslána.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Twilio with the URL mentioned here."}, "SUBMIT_BUTTON": "Vytvo<PERSON><PERSON>", "API": {"ERROR_MESSAGE": "Nebyli jsme schopni ověřit přihlašovací údaje Twilia, zkuste to prosím znovu"}}, "SMS": {"TITLE": "SMS Channel", "DESC": "Start supporting your customers via SMS.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "We were not able to save the SMS channel"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Account ID", "PLACEHOLDER": "Please enter your Bandwidth Account ID", "ERROR": "Toto pole je povinné"}, "API_KEY": {"LABEL": "API Key", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "Toto pole je povinné"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "Toto pole je povinné"}, "APPLICATION_ID": {"LABEL": "Application ID", "PLACEHOLDER": "Please enter your Bandwidth Application ID", "ERROR": "Toto pole je povinné"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "Toto pole je povinné"}, "PHONE_NUMBER": {"LABEL": "Telefonní číslo", "PLACEHOLDER": "Zadejte prosím telefonní číslo, ze kterého bude zpráva odeslána.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "SUBMIT_BUTTON": "Create Bandwidth Channel", "API": {"ERROR_MESSAGE": "We were not able to authenticate Bandwidth credentials, please try again"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Bandwidth with the URL mentioned here."}}}, "WHATSAPP": {"TITLE": "WhatsApp Channel", "DESC": "Start supporting your customers via WhatsApp.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Please enter an inbox name", "ERROR": "Toto pole je povinné"}, "PHONE_NUMBER": {"LABEL": "Telefonní číslo", "PLACEHOLDER": "Zadejte prosím telefonní číslo, ze kterého bude zpráva odeslána.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "PHONE_NUMBER_ID": {"LABEL": "Phone number ID", "PLACEHOLDER": "Please enter the Phone number ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Business Account ID", "PLACEHOLDER": "Please enter the Business Account ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Verify Token", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Please enter a valid value."}, "API_KEY": {"LABEL": "API key", "SUBTITLE": "Configure the WhatsApp API key.", "PLACEHOLDER": "API key", "ERROR": "Please enter a valid value."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL and the verification token in the Facebook Developer portal with the values shown below.", "WEBHOOK_URL": "URL webového háčku", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verification Token"}, "SUBMIT_BUTTON": "Create WhatsApp Channel", "API": {"ERROR_MESSAGE": "We were not able to save the WhatsApp channel"}}, "VOICE": {"TITLE": "Voice Channel", "DESC": "Integrate Twilio Voice and start supporting your customers via phone calls.", "PHONE_NUMBER": {"LABEL": "Telefonní číslo", "PLACEHOLDER": "Enter your phone number (e.g. +**********)", "ERROR": "Please provide a valid phone number in E.164 format (e.g. +**********)"}, "TWILIO": {"ACCOUNT_SID": {"LABEL": "SID účtu", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON> Account SID", "REQUIRED": "Account SID is required"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON>", "REQUIRED": "Auth Token is required"}, "API_KEY_SID": {"LABEL": "API Key SID", "PLACEHOLDER": "Enter your Twilio API Key SID", "REQUIRED": "API Key SID is required"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Enter your Twilio API Key Secret", "REQUIRED": "API Key Secret is required"}, "TWIML_APP_SID": {"LABEL": "TwiML App SID", "PLACEHOLDER": "Enter your Twilio TwiML App SID (starts with AP)", "REQUIRED": "TwiML App SID is required"}}, "SUBMIT_BUTTON": "Create Voice Channel", "API": {"ERROR_MESSAGE": "We were not able to create the voice channel"}}, "API_CHANNEL": {"TITLE": "API Channel", "DESC": "Integrate with API channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "Název ka<PERSON>", "PLACEHOLDER": "Zadejte název kanálu", "ERROR": "Toto pole je povinné"}, "WEBHOOK_URL": {"LABEL": "URL webového háčku", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "URL webového háčku"}, "SUBMIT_BUTTON": "Create API Channel", "API": {"ERROR_MESSAGE": "We were not able to save the api channel"}}, "EMAIL_CHANNEL": {"TITLE": "Email Channel", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "Název ka<PERSON>", "PLACEHOLDER": "Zadejte název kanálu", "ERROR": "Toto pole je povinné"}, "EMAIL": {"LABEL": "E-mailová adresa", "SUBTITLE": "Email where your customers sends you support tickets", "PLACEHOLDER": "E-mailová adresa"}, "SUBMIT_BUTTON": "Create Email Channel", "API": {"ERROR_MESSAGE": "We were not able to save the email channel"}, "FINISH_MESSAGE": "Start forwarding your emails to the following email address."}, "LINE_CHANNEL": {"TITLE": "LINE Channel", "DESC": "Integrate with LINE channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "Název ka<PERSON>", "PLACEHOLDER": "Zadejte název kanálu", "ERROR": "Toto pole je povinné"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Channel ID", "PLACEHOLDER": "LINE Channel ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Channel Token", "PLACEHOLDER": "LINE Channel Token"}, "SUBMIT_BUTTON": "Create LINE Channel", "API": {"ERROR_MESSAGE": "We were not able to save the LINE channel"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL in LINE application with the URL mentioned here."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Channel", "DESC": "Integrate with Telegram channel and start supporting your customers.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure the bot token you have obtained from Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Create Telegram Channel", "API": {"ERROR_MESSAGE": "We were not able to save the telegram channel"}}, "AUTH": {"TITLE": "Choose a channel", "DESC": "Chatwoot supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "Agenti", "DESC": "Zde můžete přidat agenty ke správě nově vytvo<PERSON><PERSON><PERSON> s<PERSON>. Pouze tito vybraní agenti budou mít přístup do va<PERSON><PERSON> schr<PERSON>. Agenty, které nejsou součástí té<PERSON>, nebudou moci při přihlášení vidět zprávy v této schránce ani na ně reagovat. <br> <b>PS:</b> <PERSON><PERSON><PERSON> spr<PERSON>, pokud potřebujete přístup ke v<PERSON>em s<PERSON>r<PERSON>, byste se měli přidat jako agent do v<PERSON><PERSON>, které vytváříte.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Pick agents for the inbox"}, "DETAILS": {"TITLE": "Doručená pošta <PERSON>y", "DESC": "Z rozevíracího seznamu níže vyberte Facebook stránku, kterou chcete připojit k Chatwoot. Můžete také zadat vlastní název doručené poště pro lepší identifikaci."}, "FINISH": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> to!", "DESC": "Úspěšně jste dokončili integraci vaš<PERSON> facebookové stránky s Chatwootem. <PERSON><PERSON> pří<PERSON>tě přijde zákaznická zpráva, konverzace se automaticky objeví ve va<PERSON><PERSON> schr<PERSON>.<br>Poskytujeme vám také widget skript, kter<PERSON> můžete snadno přidat na vaše webové stránky. Jak<PERSON> je toto aktivní na vašich webových strán<PERSON>, zákazníci vám mohou posílat zprávy přímo z vašich webových stránek bez pomoci externího nástroje a konverzace se objeví přímo zde, na Chatwoot.<br><PERSON><PERSON><PERSON><PERSON><PERSON>, co? No, určitě se pokusíme být :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "Ověřování pomocí Facebooku...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "Něco se poka<PERSON>lo, prosím obnovte stránku...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "Vytvářím vaši doručenou poštu...", "TITLE": "Konfigurace detailů doručené po<PERSON>", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Přidat agenty", "ADD_AGENTS": "Přidávání agentů do vaší s<PERSON>..."}, "FINISH": {"TITLE": "Vaše doručená pošta je př<PERSON>na!", "MESSAGE": "You can now engage with your customers through your new Channel. Happy supporting", "BUTTON_TEXT": "<PERSON><PERSON><PERSON> m<PERSON> tam", "MORE_SETTINGS": "More settings", "WEBSITE_SUCCESS": "Úspěšně jste dokončili vytvoření webového kanálu. Zkopírujte kód zobrazený níže a vložte jej na vaše webové stránky. Když zákazník příště použije živý chat, konverzace se automaticky objeví ve vaší doručené poště."}, "REAUTH": "Znovu autorizovat", "VIEW": "Zobrazit", "EDIT": {"API": {"SUCCESS_MESSAGE": "Nastavení doručené pošty bylo úspěšně aktualizováno", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Automatické přiřazení bylo úspěšně aktualizováno", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CSAT": {"ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Friendly", "FROM": "od", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Save"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "Povoleno", "DISABLED": "Zak<PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_HMAC": {"LABEL": "Enable"}}, "DELETE": {"BUTTON_TEXT": "Vymazat", "AVATAR_DELETE_BUTTON_TEXT": "Delete Avatar", "CONFIRM": {"TITLE": "Potvrdit odstranění", "MESSAGE": "Opravdu chcete odstranit ", "PLACE_HOLDER": "Please type {inboxName} to confirm", "YES": "Ano, odstranit ", "NO": "Ne, zachovat "}, "API": {"SUCCESS_MESSAGE": "Doručená pošta byla úspěšně s<PERSON>zána", "ERROR_MESSAGE": "Nelze odstranit doručenou poštu. Opakujte akci později.", "AVATAR_SUCCESS_MESSAGE": "Inbox avatar deleted successfully", "AVATAR_ERROR_MESSAGE": "Could not delete the inbox avatar. Please try again later."}}, "TABS": {"SETTINGS": "Nastavení", "COLLABORATORS": "Spolupracující", "CONFIGURATION": "Nastavení", "CAMPAIGN": "Kampaně", "PRE_CHAT_FORM": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ed chatem", "BUSINESS_HOURS": "Pracovní doba", "WIDGET_BUILDER": "Widget Builder", "BOT_CONFIGURATION": "Bot Configuration", "CSAT": "CSAT"}, "SETTINGS": "Nastavení", "FEATURES": {"LABEL": "Funkce", "DISPLAY_FILE_PICKER": "Display file picker on the widget", "DISPLAY_EMOJI_PICKER": "Display emoji picker on the widget", "ALLOW_END_CONVERSATION": "Allow users to end conversation from the widget", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON> skript", "MESSENGER_SUB_HEAD": "Umístěte toto tlačítko dovnitř vašeho tělesného štítku", "INBOX_AGENTS": "Agenti", "INBOX_AGENTS_SUB_TEXT": "Přidat nebo odebrat agenty z této složky doručené pošty", "AGENT_ASSIGNMENT": "Conversation Assignment", "AGENT_ASSIGNMENT_SUB_TEXT": "Update conversation assignment settings", "UPDATE": "Aktualizovat", "ENABLE_EMAIL_COLLECT_BOX": "Enable email collect box", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Enable or disable email collect box on new conversation", "AUTO_ASSIGNMENT": "Povolit automatické přiřazení", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "Enable conversation continuity via email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "Conversations will continue over email if the contact email address is available.", "LOCK_TO_SINGLE_CONVERSATION": "Lock to single conversation", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Enable or disable multiple conversations for the same contact in this inbox", "INBOX_UPDATE_TITLE": "Nastavení doručené p<PERSON>", "INBOX_UPDATE_SUB_TEXT": "Aktualizujte nastavení doručené pošty", "AUTO_ASSIGNMENT_SUB_TEXT": "Povolit nebo zakázat automatické přiřazování nových konverzací agentům přidaným do této <PERSON>.", "HMAC_VERIFICATION": "User Identity Validation", "HMAC_DESCRIPTION": "In order to validate the user's identity, you can pass an `identifier_hash` for each user. You can generate a HMAC sha256 hash using the `identifier` with the key shown here.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "Enforce User Identity Validation", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Inbox Identifier", "INBOX_IDENTIFIER_SUB_TEXT": "Use the `inbox_identifier` token shown here to authentication your API clients.", "FORWARD_EMAIL_TITLE": "Forward to Email", "FORWARD_EMAIL_SUB_TEXT": "Start forwarding your emails to the following email address.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Allow messages after conversation resolved", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Allow the end-users to send messages even after the conversation is resolved.", "WHATSAPP_SECTION_SUBHEADER": "This API Key is used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API Key", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "Aktualizovat", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verification Token", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Auto assignment limit", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Please enter a value greater than 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limit the maximum number of conversations from this inbox that can be auto assigned to an agent"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Znovu autorizovat", "SUBTITLE": "Your Facebook connection has expired, please reconnect your Facebook page to continue services", "MESSAGE_SUCCESS": "Reconnection successful", "MESSAGE_ERROR": "Do<PERSON><PERSON> k chybě, zkuste to prosím znovu"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pre chat forms enable you to capture user information before they start conversation with you.", "SET_FIELDS": "Pre chat form fields", "SET_FIELDS_HEADER": {"FIELDS": "Fields", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "Key", "TYPE": "Type", "REQUIRED": "Required"}, "ENABLE": {"LABEL": "Enable pre chat form", "OPTIONS": {"ENABLED": "<PERSON><PERSON>", "DISABLED": "Ne"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pre chat message", "PLACEHOLDER": "This message would be visible to the users along with the form"}, "REQUIRE_EMAIL": {"LABEL": "Visitors should provide their name and email address before starting the chat"}}, "CSAT": {"TITLE": "Enable CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "Zpráva", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "contains", "DOES_NOT_CONTAINS": "does not contain"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Nastavte svou dostupnost", "SUBTITLE": "Set your availability on your livechat widget", "WEEKLY_TITLE": "Set your weekly hours", "TIMEZONE_LABEL": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "UPDATE": "Update business hours settings", "TOGGLE_AVAILABILITY": "Enable business availability for this inbox", "UNAVAILABLE_MESSAGE_LABEL": "Unavailable message for visitors", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Enable availability for this day", "UNAVAILABLE": "Nedostupný", "HOURS": "hodiny", "VALIDATION_ERROR": "Starting time should be before closing time.", "CHOOSE": "<PERSON><PERSON><PERSON><PERSON>"}, "ALL_DAY": "All-Day"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Set your IMAP details", "NOTE_TEXT": "To enable SMTP, please configure IMAP.", "UPDATE": "Update IMAP settings", "TOGGLE_AVAILABILITY": "Enable IMAP configuration for this inbox", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP settings updated successfully", "ERROR_MESSAGE": "Unable to update IMAP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Přihlásit se", "PLACE_HOLDER": "Přihlásit se"}, "PASSWORD": {"LABEL": "He<PERSON><PERSON>", "PLACE_HOLDER": "He<PERSON><PERSON>"}, "ENABLE_SSL": "Enable SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Set your SMTP details", "UPDATE": "Update SMTP settings", "TOGGLE_AVAILABILITY": "Enable SMTP configuration for this inbox", "TOGGLE_HELP": "Enabling SMTP will help the user to send email", "EDIT": {"SUCCESS_MESSAGE": "SMTP settings updated successfully", "ERROR_MESSAGE": "Unable to update SMTP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Přihlásit se", "PLACE_HOLDER": "Přihlásit se"}, "PASSWORD": {"LABEL": "He<PERSON><PERSON>", "PLACE_HOLDER": "He<PERSON><PERSON>"}, "DOMAIN": {"LABEL": "Domain", "PLACE_HOLDER": "Domain"}, "ENCRYPTION": "Encryption", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Mode", "AUTH_MECHANISM": "Authentication"}, "NOTE": "Note: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar deleted successfully", "ERROR_MESSAGE": "Do<PERSON><PERSON> k chybě, zkuste to prosím znovu"}}}, "WEBSITE_NAME": {"LABEL": "Website Name", "PLACE_HOLDER": "Enter your website name (eg: Acme Inc)", "ERROR": "Please enter a valid website name"}, "WELCOME_HEADING": {"LABEL": "Úvod uvítání", "PLACE_HOLDER": "Hi there!"}, "WELCOME_TAGLINE": {"LABEL": "Vítejte Tagline", "PLACE_HOLDER": "Snadno se s námi spojujeme. Požádejte nás o cokoliv, nebo sdílejte svou zpětnou vazbu."}, "REPLY_TIME": {"LABEL": "Reply Time", "IN_A_FEW_MINUTES": "Do několika minut", "IN_A_FEW_HOURS": "Do několika hodin", "IN_A_DAY": "Do dne"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON> widgetu", "WIDGET_BUBBLE_POSITION_LABEL": "Widget Bubble Position", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Type", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "Na<PERSON>š<PERSON><PERSON> nám", "LABEL": "Widget Bubble Launcher Title", "PLACE_HOLDER": "Na<PERSON>š<PERSON><PERSON> nám"}, "UPDATE": {"BUTTON_TEXT": "Update Widget Settings", "API": {"SUCCESS_MESSAGE": "Widget settings updated successfully", "ERROR_MESSAGE": "Unable to update widget settings"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "<PERSON><PERSON><PERSON><PERSON>", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Left", "RIGHT": "Right"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standard", "EXPANDED_BUBBLE": "Expanded Bubble"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Větš<PERSON>u odpoví<PERSON> b<PERSON><PERSON> pár minut", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON><PERSON><PERSON>u od<PERSON>v<PERSON><PERSON> b<PERSON><PERSON> pár hodin", "IN_A_DAY": "O<PERSON>vykle odpoví za den"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "CHAT_INPUT_PLACEHOLDER": "Zde začně<PERSON>t"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "We are Online", "OFFLINE": "V současné době jsme pryč"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hello"}, "BRANDING_TEXT": "Powered by <PERSON><PERSON><PERSON><PERSON>", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "E-mailová adresa", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Channel", "INSTAGRAM": "Instagram", "VOICE": "Voice"}}}