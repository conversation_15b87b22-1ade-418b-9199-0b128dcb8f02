{"MACROS": {"HEADER": "Μακροεντολές", "DESCRIPTION": "A macro is a set of saved actions that help customer service agents easily complete tasks. The agents can define a set of actions like tagging a conversation with a label, sending an email transcript, updating a custom attribute, etc., and they can run these actions in a single click.", "LEARN_MORE": "Learn more about macros", "HEADER_BTN_TXT": "Προσθήκη νέας μακροεντολής", "HEADER_BTN_TXT_SAVE": "Αποθήκευση μακροεντολής", "LOADING": "Λήψη μακροεντολών", "ERROR": "Κάτι πήγε στραβά. Παρακ<PERSON><PERSON><PERSON> προσπαθήστε ξανά", "ORDER_INFO": "Μακροεντολές θα εκτελεστούν με τη σειρά που θα προσθέσετε τις ενέργειές σας. Μπορείτε να τις αναδιατάξετε σύροντάς τις από τη λαβή δίπλα σε κάθε κόμβο.", "ADD": {"FORM": {"NAME": {"LABEL": "Όνομα μακροεντολής", "PLACEHOLDER": "Εισάγετε ένα όνομα για την μακροεντολή σας", "ERROR": "Το όνομα απαιτείται για τη δημιουργία μακροεντολής"}, "ACTIONS": {"LABEL": "Ενέργειες"}}, "API": {"SUCCESS_MESSAGE": "Η μακροεντολή προστέθηκε επιτυχώς", "ERROR_MESSAGE": "Δεν είναι δυνατή η δημιουργ<PERSON>α μακροεντολής, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> προσπαθήστε ξανά αργότερα"}}, "LIST": {"TABLE_HEADER": {"NAME": "Όνομα", "CREATED BY": "Δημιουργήθηκε από", "LAST_UPDATED_BY": "Τελευτα<PERSON>α ενημέρωση από", "VISIBILITY": "Ορατότητα"}, "404": "Δεν βρέθηκαν μακροεντολές"}, "DELETE": {"TOOLTIP": "Διαγρα<PERSON><PERSON> μακροεντολής", "CONFIRM": {"MESSAGE": "Είσαστε σίγουροι για την διαγραφή ", "YES": "Ναι, Διέγραψε το", "NO": "Όχι"}, "API": {"SUCCESS_MESSAGE": "Το hook διαγράφηκε επιτυχώς", "ERROR_MESSAGE": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά τη διαγραφή της μακροεντολής. Πα<PERSON>α<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά αργότερα"}}, "EDIT": {"TOOLTIP": "Επεξεργα<PERSON><PERSON>α μακροεντολής", "API": {"SUCCESS_MESSAGE": "Ο πράκτορας ενημερώθηκε επιτυχώς", "ERROR_MESSAGE": "Δεν είναι δυνατή η ενημέρωση της μακροεντολής, <PERSON><PERSON>ρα<PERSON><PERSON><PERSON><PERSON> προσπαθήστε ξανά αργότερα"}}, "EDITOR": {"START_FLOW": "Έναρξη Ροής", "END_FLOW": "Τέλος Ροής", "LOADING": "<PERSON>ή<PERSON>η μακροεντολής", "ADD_BTN_TOOLTIP": "Προσθήκη νέας ενέργειας", "DELETE_BTN_TOOLTIP": "Διαγραφή Ενέργειας", "VISIBILITY": {"LABEL": "Ορατότητα Μακροεντολής", "GLOBAL": {"LABEL": "Δημόσια", "DESCRIPTION": "Αυτή η μακροεντολή είναι διαθέσιμη δημοσίως για όλους τους πράκτορες σε αυτόν τον λογαριασμό."}, "PERSONAL": {"LABEL": "Ιδιωτική", "DESCRIPTION": "Αυτή η μακροεντολή θα είναι ιδιωτική σε εσάς και δεν θα είναι διαθέσιμη σε άλλους."}}}, "EXECUTE": {"BUTTON_TOOLTIP": "Εκτέλεση", "PREVIEW": "Προεπισκόπηση Μακροεντολής", "EXECUTED_SUCCESSFULLY": "Η μακροεντολή εκτελέστηκε επιτυχώς"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_REQUIRED": "Απαιτείται τιμή", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998", "ACTION_PARAMETERS_REQUIRED": "Action parameters are required", "ATLEAST_ONE_CONDITION_REQUIRED": "At least one condition is required", "ATLEAST_ONE_ACTION_REQUIRED": "At least one action is required"}, "ACTIONS": {"ASSIGN_TEAM": "Assign a Team", "ASSIGN_AGENT": "Assign an Agent", "ADD_LABEL": "Add a Label", "REMOVE_LABEL": "Remove a Label", "REMOVE_ASSIGNED_TEAM": "Remove Assigned Team", "SEND_EMAIL_TRANSCRIPT": "Send an Email Transcript", "MUTE_CONVERSATION": "Σίγαση Συνομιλίας", "SNOOZE_CONVERSATION": "Αναβολή Συνομιλίας", "RESOLVE_CONVERSATION": "Επίλυση Συνομιλίας", "SEND_ATTACHMENT": "Send Attachment", "SEND_MESSAGE": "Send a Message", "CHANGE_PRIORITY": "Change Priority", "ADD_PRIVATE_NOTE": "Add a Private Note", "SEND_WEBHOOK_EVENT": "Send Webhook Event"}}}