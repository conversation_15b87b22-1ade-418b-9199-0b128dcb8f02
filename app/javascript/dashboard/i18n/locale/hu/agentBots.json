{"AGENT_BOTS": {"HEADER": "Botok", "LOADING_EDITOR": "Szerkesztő betöltése...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Rendszer", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Válassz ügynököt", "DESC": "Rendeljen egy Agent Botot a postaládájához. Ők képesek kezelni a kezdeti beszélgetéseket, és szükség esetén átirányítani őket egy emberhez.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "<PERSON><PERSON>", "SUCCESS_MESSAGE": "Ügynök frissítve.", "DISCONNECTED_SUCCESS_MESSAGE": "Ügynök leválasztva.", "ERROR_MESSAGE": "<PERSON><PERSON> f<PERSON>i a botot. K<PERSON>r<PERSON><PERSON>k, próbá<PERSON>ja meg ú<PERSON>.", "DISCONNECTED_ERROR_MESSAGE": "<PERSON><PERSON> le<PERSON>tani az botot. K<PERSON>rj<PERSON>k, próbá<PERSON>ja meg ú<PERSON>.", "SELECT_PLACEHOLDER": "Válassza ki a botot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON>.", "ERROR_MESSAGE": "<PERSON><PERSON> tudott botot hozzáadni. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pr<PERSON><PERSON><PERSON><PERSON><PERSON> meg később újra."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Botok hívása...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "Törlés", "TITLE": "Bot törlése", "CONFIRM": {"TITLE": "Törlés megerősítése", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Igen, Törlés", "NO": "<PERSON><PERSON>, Mégse"}, "API": {"SUCCESS_MESSAGE": "Bot törölve.", "ERROR_MESSAGE": "<PERSON><PERSON> törölni a botot. Kérjük, próbálja <PERSON>."}}, "EDIT": {"BUTTON_TEXT": "Szerkesztés", "TITLE": "Bot szerkesztése", "API": {"SUCCESS_MESSAGE": "Bot frissítve.", "ERROR_MESSAGE": "<PERSON>em tudta friss<PERSON>teni a botot. K<PERSON>rj<PERSON>k, próbá<PERSON>ja <PERSON>."}}, "ACCESS_TOKEN": {"TITLE": "Hozzáférési kulcs", "DESCRIPTION": "Copy the access token and save it securely", "COPY_SUCCESSFUL": "Access token copied to clipboard", "RESET_SUCCESS": "Access token regenerated successfully", "RESET_ERROR": "Unable to regenerate access token. Please try again"}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot neve", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Bot név megadása kötelező"}, "DESCRIPTION": {"LABEL": "Le<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Mit c<PERSON>l ez a bot?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Bot név megadása kötelező", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}