{"AGENT_BOTS": {"HEADER": "Botai", "LOADING_EDITOR": "Įkeliama redagavimo priemonė...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistema", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Pasirinkite agento botą", "DESC": "Assign an Agent Bot to your inbox. They can handle initial conversations and transfer them to a live agent when necessary.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "Atsijungti Botą", "SUCCESS_MESSAGE": "Sėkmingai atnaujintas agento botas.", "DISCONNECTED_SUCCESS_MESSAGE": "Sėkmingai atjungtas agento botas.", "ERROR_MESSAGE": "Nepavyko at<PERSON>jinti agento boto. Bandykite dar kartą vėliau.", "DISCONNECTED_ERROR_MESSAGE": "Nepavyko ajungti agento boto. Bandykite dar kartą vėliau.", "SELECT_PLACEHOLDER": "Pasirinkti Bo<PERSON>ą"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Botas pridėtas sėkmingai.", "ERROR_MESSAGE": "Nepavyko p<PERSON> boto. Bandykite dar kartą vėliau."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Gaunami Botai...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Taip, Ištrinti", "NO": "Ne, Išsaugoti"}, "API": {"SUCCESS_MESSAGE": "Botas ištrintas <PERSON>.", "ERROR_MESSAGE": "Nepavyko ištrinti boto. Bandykite dar kartą."}}, "EDIT": {"BUTTON_TEXT": "Red<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Botas atnaujintas sėkmingai.", "ERROR_MESSAGE": "Nepavyko <PERSON> boto. Bandykite dar kartą vėliau."}}, "ACCESS_TOKEN": {"TITLE": "Prieeigos r<PERSON>", "DESCRIPTION": "Copy the access token and save it securely", "COPY_SUCCESSFUL": "Access token copied to clipboard", "RESET_SUCCESS": "Access token regenerated successfully", "RESET_ERROR": "Unable to regenerate access token. Please try again"}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter bot name", "REQUIRED": "<PERSON><PERSON>"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ką daro šis botas?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "<PERSON><PERSON>", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Boto Webhook"}}}