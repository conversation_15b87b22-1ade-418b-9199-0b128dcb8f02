{"AGENT_BOTS": {"HEADER": "Roboti", "LOADING_EDITOR": "Notiek redaktora ielāde...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON> par aģentiem robotiem", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "<PERSON><PERSON><PERSON><PERSON>", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Izvēlieties aģentu robotu", "DESC": "Piešķiriet savai iesūtnei Aģentu Robotu. Viņi var apstrādāt sākotnējās sarunas un vajadzības gadījumā tās pārsūtīt tiešajam aģentam.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "<PERSON><PERSON><PERSON>u", "SUCCESS_MESSAGE": "Aģents robots ir ve<PERSON><PERSON><PERSON><PERSON> at<PERSON>.", "DISCONNECTED_SUCCESS_MESSAGE": "Aģenta robots ir veik<PERSON><PERSON><PERSON> atvienots.", "ERROR_MESSAGE": "Nevar<PERSON><PERSON> at<PERSON>t aģenta robotu. Lūdzu mēģiniet vēlreiz.", "DISCONNECTED_ERROR_MESSAGE": "Nevarēja atvienot aģenta robotu. Lūdzu mēģiniet vēlreiz.", "SELECT_PLACEHOLDER": "Izvēlieties robotu"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Atcelt", "API": {"SUCCESS_MESSAGE": "Robots ir ve<PERSON><PERSON><PERSON><PERSON> pievie<PERSON>s.", "ERROR_MESSAGE": "Nevarēja pievie<PERSON>u. <PERSON><PERSON><PERSON><PERSON>, pamēģiniet vēlāk vēlreiz."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Notiek robotu iegūšana...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON> robotu", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Jā, Dzēst", "NO": "Nē, Paturēt"}, "API": {"SUCCESS_MESSAGE": "Robots ir veik<PERSON> i<PERSON>.", "ERROR_MESSAGE": "Nevarēja izdzēst robotu. Lūdzu mēģiniet vēlreiz."}}, "EDIT": {"BUTTON_TEXT": "Rediģēt", "TITLE": "Rediģēt robotu", "API": {"SUCCESS_MESSAGE": "Robots ir ve<PERSON><PERSON><PERSON><PERSON>.", "ERROR_MESSAGE": "Nevar<PERSON><PERSON> at<PERSON>u. Lūdzu mēģiniet vēlreiz."}}, "ACCESS_TOKEN": {"TITLE": "Piekļ<PERSON><PERSON>", "DESCRIPTION": "Copy the access token and save it securely", "COPY_SUCCESSFUL": "Access token copied to clipboard", "RESET_SUCCESS": "Access token regenerated successfully", "RESET_ERROR": "Unable to regenerate access token. Please try again"}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Robota no<PERSON>ukums", "PLACEHOLDER": "Enter bot name", "REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON> robota no<PERSON>uku<PERSON>"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ko dara šis robots?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON> robota no<PERSON>uku<PERSON>", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Atcelt", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook robots"}}}