{"INBOX_MGMT": {"HEADER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON>n<PERSON><PERSON> ir saziņas veids, ko k<PERSON>s izvēlas saziņai ar jums. <PERSON><PERSON><PERSON><PERSON><PERSON> ir vieta, kur pārvald<PERSON>t mi<PERSON>edarbības ar noteiktu kan<PERSON>lu. Tas var ietvert saziņu no dažādiem avotiem, pie<PERSON><PERSON><PERSON>, e-pasta, tie<PERSON><PERSON><PERSON>s tērzēša<PERSON> un sociālajiem medijiem.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par iesūtnēm", "RECONNECTION_REQUIRED": "<PERSON><PERSON><PERSON> i<PERSON> ir atvienota. <PERSON><PERSON><PERSON> j<PERSON>, kamēr nebū<PERSON>t tos atkārtoti autorizējis.", "CLICK_TO_RECONNECT": "Noklikšķiniet šeit, lai atkārtoti izveidotu savienojumu.", "LIST": {"404": "<PERSON><PERSON> k<PERSON>am nav pievienota neviena <PERSON>."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "Izvēlieties Kanālu", "BODY": "Izvēlieties pakalpojumu sniedz<PERSON>, kuru vēlaties integrēt ar Chatwoot."}, "INBOX": {"TITLE": "<PERSON>zve<PERSON>t <PERSON>", "BODY": "Autentificēt savu kontu un izveidot iesūtni."}, "AGENT": {"TITLE": "Pievienot Aģentus", "BODY": "Pievienojiet aģentus izveidotajai iesūtnei."}, "FINISH": {"TITLE": "Gatavs!", "BODY": "Jūs varat sākt darboties!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ievadiet Jūsu iesūtnes nosaukumu (piemēram: Acme Inc)", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu iesūtnes nosaukumu"}, "WEBSITE_NAME": {"LABEL": "Tīmekļa Vietnes No<PERSON>ukums", "PLACEHOLDER": "Ievadiet Jū<PERSON> tīmekļa vietnes nosaukumu (piemēram: Acme Inc)"}, "FB": {"HELP": "PS: Pieraks<PERSON><PERSON> mēs iegūstam piekļuvi tikai pie Jūsu lapas ziņojumiem. Chatwoot nekad nevar piekļūt jūsu privātajām ziņām.", "CHOOSE_PAGE": "Izvēlieties Lapu", "CHOOSE_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapu no saraksta", "INBOX_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD_NAME": "Pievienojiet savas iesūtnes nosaukumu", "PICK_NAME": "<PERSON><PERSON><PERSON><PERSON> ievadiet Iesūtnes nosaukumu", "PICK_A_VALUE": "Izvēlieties vērtību", "CREATE_INBOX": "<PERSON>zve<PERSON>t <PERSON>"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Pievienot savu Instagram Profilu", "HELP": "<PERSON> pievie<PERSON>u savu Instagram profilu kā kanālu, jums ir jāautentificē savs Instagram profils, noklikšķinot uz 'Turpināt ar Instagram' ", "ERROR_MESSAGE": "Veidojot sa<PERSON> ar Instagram, rad<PERSON>s k<PERSON>. Lūd<PERSON>, mēģiniet vēlreiz", "ERROR_AUTH": "Veidojot sa<PERSON> ar Instagram, rad<PERSON>s k<PERSON>. Lūd<PERSON>, mēģiniet vēlreiz", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "<PERSON>vie<PERSON> savu Twitter profilu kā kanālu, Ju<PERSON> ir jāautentificē savs Twitter profils, noklikšķinot uz \"Pierakstīties ar Twitter\"' ", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON>, veido<PERSON>t savienoju<PERSON> ar Twitter. Lūdzu, mēģiniet vēlreiz", "TWEETS": {"ENABLE": "Izveidot sarunas no minētajiem Tvītiem"}}, "WEBSITE_CHANNEL": {"TITLE": "Tīmekļa vietnes kanāls", "DESC": "Izveidot savai tīmekļa vietnei kanālu un sākt atbalstīt savus k<PERSON>, i<PERSON><PERSON><PERSON><PERSON> mūsu tīmekļu vietnes logrīku.", "LOADING_MESSAGE": "Tiek Veikta Tīmekļa Vietnes Atbalsta Kanāla Izveide", "CHANNEL_AVATAR": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "L<PERSON><PERSON>zu ievadiet Webhook URL", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu URL"}, "CHANNEL_DOMAIN": {"LABEL": "Tīmekļa Vietnes Domē<PERSON>", "PLACEHOLDER": "Ievadiet savas tīmekļa viet<PERSON> do<PERSON>ē<PERSON> (piemēram: acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "<PERSON><PERSON><PERSON> <PERSON>", "PLACEHOLDER": "Sveicināti !"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Laipni lūd<PERSON>", "PLACEHOLDER": "Sazināties ar mums ir vien<PERSON><PERSON><PERSON><PERSON>. Vaicājiet mums j<PERSON><PERSON>, vai dalieties ar savām atsauksmēm."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Ka<PERSON><PERSON>la sveici<PERSON> p<PERSON>", "PLACEHOLDER": "Acme Inc parasti atbild dažu stundu laikā."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kan<PERSON>la sveicienu", "HELP_TEXT": "Automātiski no<PERSON>ūtīt sveiciena <PERSON>, kad tiek izveidota jauna saruna.", "ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "REPLY_TIME": {"TITLE": "Iestatīt Atbildes laiku", "IN_A_FEW_MINUTES": "<PERSON><PERSON><PERSON><PERSON>", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "IN_A_DAY": "<PERSON><PERSON>", "HELP_TEXT": "Šis atbildes laiks tiks parādīts tiešsaistes tērzēša<PERSON> logrī<PERSON>"}, "WIDGET_COLOR": {"LABEL": "Logrīka <PERSON>", "PLACEHOLDER": "Atjaunināt logrīkā izmantoto logrīka krāsu"}, "SUBMIT_BUTTON": "Izveidot iesūtni", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m izve<PERSON>t tīmekļa vietnes kanālu. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Ka<PERSON>", "DESC": "Integrēt T<PERSON>lio un sākt atbalstīt savu<PERSON> k<PERSON>, izmantojot SMS vai WhatsApp.", "ACCOUNT_SID": {"LABEL": "Konta SID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet sava Twilio konta SID", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "API_KEY": {"USE_API_KEY": "Izmantot API Atslēgas Autentifikāciju", "LABEL": "API Atslēgas SID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu API Atslēgas SID", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "API_KEY_SECRET": {"LABEL": "API Atslēgas Noslēpums", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu API Atslēgas Noslēpumu", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "MESSAGING_SERVICE_SID": {"LABEL": "Ziņapmaiņas Pakalpojuma SID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu <PERSON><PERSON>as pakalpojuma SID", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>", "USE_MESSAGING_SERVICE": "Izmantot <PERSON>alpo<PERSON>"}, "CHANNEL_TYPE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, izv<PERSON>lie<PERSON> sava Ka<PERSON>"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu <PERSON><PERSON><PERSON>", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet iesūt<PERSON> no<PERSON>mu", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "PHONE_NUMBER": {"LABEL": "Telefona numurs", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ieva<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> numuru, no kura tiks nosūt<PERSON>ts ziņ<PERSON>.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> derīgu t<PERSON>, kas sākas ar <PERSON>+” un nesatur atstarpes."}, "API_CALLBACK": {"TITLE": "Atzvanīšanas URL", "SUBTITLE": "Jums ir jānokonfigur<PERSON> ziņoju<PERSON> atzvanīšanas URL, izmantojot šeit minēto URL."}, "SUBMIT_BUTTON": "<PERSON>zve<PERSON><PERSON>", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> ne<PERSON>m autentificēt Twilio akreditācijas datus. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz"}}, "SMS": {"TITLE": "SMS Kanāls", "DESC": "Sāciet atbalstī<PERSON> sa<PERSON>, izmantojot SMS.", "PROVIDERS": {"LABEL": "API Pakalpojumu Sniedzējs", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> sa<PERSON> kanālu"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Konta ID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu Bandwidth konta ID", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "API_KEY": {"LABEL": "API atslēga", "PLACEHOLDER": "Lūdzu ievadiet Bandwidth API Key", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Lūdzu ievadiet Bandwidth API Secret", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "APPLICATION_ID": {"LABEL": "Lietojumprogrammas ID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu Bandwidth lietojumprogrammas ID", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet iesūt<PERSON> no<PERSON>mu", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "PHONE_NUMBER": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ieva<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> numuru, no kura tiks nosūt<PERSON>ts ziņ<PERSON>.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> derīgu t<PERSON>, kas sākas ar <PERSON>+” un nesatur atstarpes."}, "SUBMIT_BUTTON": "Izveidot Bandwidth kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON> ne<PERSON>j<PERSON>m autentificēt Bandwidth akreditācijas datus. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz"}, "API_CALLBACK": {"TITLE": "Atzvanīšanas URL", "SUBTITLE": "Jums ir jānokonfigurē Bandwidth ziņojumu atzvanīšanas URL, izmantojot šeit minēto URL."}}}, "WHATSAPP": {"TITLE": "<PERSON>s<PERSON><PERSON>", "DESC": "<PERSON><PERSON><PERSON><PERSON> atbal<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> WhatsApp.", "PROVIDERS": {"LABEL": "API Pakalpojumu Sniedzējs", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet iesūt<PERSON> no<PERSON>mu", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "PHONE_NUMBER": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ieva<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> numuru, no kura tiks nosūt<PERSON>ts ziņ<PERSON>.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> derīgu t<PERSON>, kas sākas ar <PERSON>+” un nesatur atstarpes."}, "PHONE_NUMBER_ID": {"LABEL": "Tālruņa numura ID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ieva<PERSON>t Tālruņa numura ID, kas iegūts no Facebook izstrādātāja informācijas paneļa.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu vērt<PERSON>."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Biznesa Konta ID", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet Biznesa Konta ID, kas iegūts no Facebook izstrādātāja informācijas paneļa.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu vērt<PERSON>."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Pārb<PERSON><PERSON>", "PLACEHOLDER": "Ievadiet verifikācijas token, ko vēlaties izmantot priekš Facebook webhook.", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu vērt<PERSON>."}, "API_KEY": {"LABEL": "API atslēga", "SUBTITLE": "Nokonfigurējiet WhatsApp API atslēgu.", "PLACEHOLDER": "API atslēga", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu vērt<PERSON>."}, "API_CALLBACK": {"TITLE": "Atzvanīšanas URL", "SUBTITLE": "Jums Facebook izstrādātāju portālā ir jānokonfigurē webhoot URL un verifikācijas token ar tālāk norādītajām vērtībām.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verifikācijas Token"}, "SUBMIT_BUTTON": "Izveidot WhatsApp kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m saglab<PERSON>t WhatsApp kanālu"}}, "VOICE": {"TITLE": "Voice Channel", "DESC": "Integrate Twilio Voice and start supporting your customers via phone calls.", "PHONE_NUMBER": {"LABEL": "Telefona numurs", "PLACEHOLDER": "Enter your phone number (e.g. +**********)", "ERROR": "Please provide a valid phone number in E.164 format (e.g. +**********)"}, "TWILIO": {"ACCOUNT_SID": {"LABEL": "Konta SID", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON> Account SID", "REQUIRED": "Account SID is required"}, "AUTH_TOKEN": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter your <PERSON><PERSON><PERSON>", "REQUIRED": "Auth Token is required"}, "API_KEY_SID": {"LABEL": "API Atslēgas SID", "PLACEHOLDER": "Enter your Twilio API Key SID", "REQUIRED": "API Key SID is required"}, "API_KEY_SECRET": {"LABEL": "API Atslēgas Noslēpums", "PLACEHOLDER": "Enter your Twilio API Key Secret", "REQUIRED": "API Key Secret is required"}, "TWIML_APP_SID": {"LABEL": "TwiML App SID", "PLACEHOLDER": "Enter your Twilio TwiML App SID (starts with AP)", "REQUIRED": "TwiML App SID is required"}}, "SUBMIT_BUTTON": "Create Voice Channel", "API": {"ERROR_MESSAGE": "We were not able to create the voice channel"}}, "API_CHANNEL": {"TITLE": "API Kanāls", "DESC": "Integrēt ar API kanālu un sākt atbalstīt savus k<PERSON>us.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet kan<PERSON><PERSON> no<PERSON>", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Konfigurēt URL, kurā vēlaties saņemt notikumu atzvanus.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Izveidot API kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m sagla<PERSON>t <PERSON> kanālu"}}, "EMAIL_CHANNEL": {"TITLE": "E-pasta Kan<PERSON>ls", "DESC": "Integrēt sava e-pasta iesūtni.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet kan<PERSON><PERSON> no<PERSON>", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "EMAIL": {"LABEL": "E-pasts", "SUBTITLE": "Norādiet e-pasta adresi, uz kuru <PERSON><PERSON><PERSON> klienti sūta atbalsta pieprasījumus.", "PLACEHOLDER": "E-pasts"}, "SUBMIT_BUTTON": "Izveidot E-pasta Kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m saglabāt e-pasta kanālu"}, "FINISH_MESSAGE": "Sākt pā<PERSON><PERSON>t<PERSON>t savus e-pasta ziņojumus uz tālāk norādīto e-pasta adresi."}, "LINE_CHANNEL": {"TITLE": "LINE <PERSON>", "DESC": "Integrēt ar LINE kanālu un sākt atbalstīt savus k<PERSON>us.", "CHANNEL_NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet kan<PERSON><PERSON> no<PERSON>", "ERROR": "<PERSON><PERSON> la<PERSON> ir <PERSON>"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Kanāla ID", "PLACEHOLDER": "LINE Kanāla ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Kanāla Secret", "PLACEHOLDER": "LINE Kanāla Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Kanāla <PERSON>", "PLACEHOLDER": "LINE Kanāla <PERSON>"}, "SUBMIT_BUTTON": "Izveidot LINE Kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m saglabāt LINE kanālu"}, "API_CALLBACK": {"TITLE": "Atzvanīšanas URL", "SUBTITLE": "Jums LINE lietojumprogrammā ir jānokonfigurē webhook URL, ar šeit minēto URL."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram <PERSON>", "DESC": "Integrēt ar Telegram kanālu un sākt atbalstīt savus k<PERSON>us.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Kon<PERSON><PERSON><PERSON><PERSON><PERSON> bot token, ko ieguvāt no Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Izveidot Telegram Kanālu", "API": {"ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m saglab<PERSON>t Telegram kanālu"}}, "AUTH": {"TITLE": "Izvēlieties kanālu", "DESC": "Chatwoot atbalsta tiešraides tērz<PERSON><PERSON>, Facebook Messenger, Twitter profilus, WhatsApp, e-pastus kā kanālus. Ja vēlaties izveidot pielāgotu kanālu, varat to izveidot, izmantojot API kanālu. <PERSON> s<PERSON>, izvēlieties vienu no tālāk norādītajiem kanāliem."}, "AGENTS": {"TITLE": "Aģenti", "DESC": "Šeit Jūs varat pievienot aģentus, lai pārvaldītu savu jaunizveidoto iesūtni. Tikai šiem atlasītajiem aģentiem būs piekļuve Jūsu iesūtnei. Aģenti, kas neietil<PERSON>t šajā iesūtnē, nevarēs redzēt ziņojumus vai atbildēt uz ziņojumiem šajā iesūtnē, kad būs pierakstījušies sistēmā. <br> <b>PS:</b> Ja jums kā administratoram ir nepieciešama piekļuve pie visām iesūtnēm, jums ir jāpievieno sevi kā aģentu visām izveidotajām iesūtnēm.", "VALIDATION_ERROR": "Pievienojiet savai jaunajai iesūtnei vismaz vienu aģentu", "PICK_AGENTS": "Izvēlieties aģentus priekš i<PERSON>nes"}, "DETAILS": {"TITLE": "Informācija par I<PERSON>ni", "DESC": "Tālāk esošajā no<PERSON>amajā izvēlnē izvēlieties Facebook lapu, kuru vēlaties savienot ar Chatwoot. Jūs varat savai ies<PERSON>i, lai to labāk at<PERSON>, piešķirt pielāgotu nosaukumu."}, "FINISH": {"TITLE": "Sanāca!", "DESC": "Jūs esat veiksmīgi pabeidzis savas Facebook lapas integrēšanu ar Chatwoot. <PERSON><PERSON>ka<PERSON><PERSON><PERSON>, kad klients nosūtīs ziņoju<PERSON> lapai, saruna automātiski parādīsies Jūsu iesūtnē.<br><PERSON><PERSON><PERSON>m Jums arī logr<PERSON> sk<PERSON>, ko varat viegli pievienot savai tīmekļa vietnei. Kad tas būs pieejams Jūsu vietnē, klienti varēs Jums nosūtīt ziņojumus tieši no Jūsu tīmekļa vietnes, neizman<PERSON><PERSON><PERSON> ārēju<PERSON> rī<PERSON>, un saruna tiks parādīta tieši šeit, vietn<PERSON> Chatwoot.<br>"}, "EMAIL_PROVIDER": {"TITLE": "Izvēlieties savu e-pasta pakalpojumu sniedzēju", "DESCRIPTION": "Tālāk esošajā sarakstā izvēlieties e-pasta pakalpojumu sniedzēju. Ja sarakstā neredzat savu e-pasta pakalpojumu sniedzēju, varat izvēlēties citu pakalpojumu sniedzēja opciju un norādīt IMAP un SMTP akreditācijas datus."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "<PERSON>, noklikšķiniet uz pogas Pier<PERSON>tīties ar Microsoft. J<PERSON>s tiksit novirzīts uz e-pasta pierakstīšanās lapu. <PERSON><PERSON> bū<PERSON>t pieņēmis pieprasītās at<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tiksit novirz<PERSON>ts atpakaļ uz iesūtnes izveides darbību.", "EMAIL_PLACEHOLDER": "Ievadiet e-pasta adresi", "SIGN_IN": "Pierakstīties ar Microsoft", "ERROR_MESSAGE": "Veidojot sa<PERSON> ar <PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>"}, "GOOGLE": {"TITLE": "Google e-pasts", "DESCRIPTION": "<PERSON>, noklikšķiniet uz pogas Pierakstīties ar Google. Jūs tiksit novirzīts uz e-pasta pierakstīšanās lapu. Kad būsiet pieņēmis pieprasītās atļ<PERSON><PERSON>, jūs tiksit novirzīts atpakaļ uz iesūtnes izveides darbību.", "SIGN_IN": "Pierakstīties ar Google", "EMAIL_PLACEHOLDER": "Ievadiet e-pasta adresi", "ERROR_MESSAGE": "Veidojot sa<PERSON> ar <PERSON>, rad<PERSON><PERSON>. L<PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>"}}, "DETAILS": {"LOADING_FB": "Notiek <PERSON>, izmantojot Facebook...", "ERROR_FB_LOADING": "Ielādējot Facebook SDK, radās k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, atspējojiet visus reklāmu bloķētājus un mēģiniet vēlreiz no citas pārlūkprogrammas.", "ERROR_FB_AUTH": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, atsvaidziniet lapu...", "ERROR_FB_UNAUTHORIZED": "Jums nav tiesību veikt šo darbību. ", "ERROR_FB_UNAUTHORIZED_HELP": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON><PERSON> ir pilna piekļuve Facebook lapai. Vairāk par Facebook lomām varat lasīt <a href=\" https://www.facebook.com/help/187316341316631\">šeit</a>.", "CREATING_CHANNEL": "Notiek Iesūtnes izveide...", "TITLE": "Nokonfigurēt Iesūtnes Informāciju", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Pievienot aģentus", "ADD_AGENTS": "Notiek aģentu pievienoša<PERSON>..."}, "FINISH": {"TITLE": "<PERSON><PERSON><PERSON> ir gatava!", "MESSAGE": "Tagad Jū<PERSON> varat i<PERSON>tot savu jauno Kan<PERSON> lai sazinātos ar saviem klientiem. Priecīgu <PERSON>bal<PERSON>", "BUTTON_TEXT": "Iet uz", "MORE_SETTINGS": "<PERSON><PERSON><PERSON><PERSON>", "WEBSITE_SUCCESS": "<PERSON>ūs esat veiksmīgi pabeidzis tīmekļa vietnes kanāla izveidi. Nokopējiet tālāk redzamo kodu un ievietojiet to savā tīmekļa vietnē. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad k<PERSON>s i<PERSON> tie<PERSON>, saruna automātiski tiks parādīta Jū<PERSON> iesūtnē."}, "REAUTH": "Atkārtoti autorizēties", "VIEW": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EDIT": {"API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> iestatīju<PERSON> ir veik<PERSON>gi at<PERSON>", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Automātiskā piešķiršana ir veik<PERSON>īgi at<PERSON>ta", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON>m at<PERSON>t iesūtnes iestatījumus. L<PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz."}, "EMAIL_COLLECT_BOX": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CSAT": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SENDER_NAME_SECTION": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>", "SUB_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vārdu, kas tiks rādīts jūsu k<PERSON>, sa<PERSON><PERSON>ot e-pasta ziņojumus no jūsu aģentiem.", "FOR_EG": "Piemēram:", "FRIENDLY": {"TITLE": "Draudzīgs", "FROM": "no", "SUBTITLE": "Sūtīt<PERSON>ja vārdā pievienojiet tā aģenta vārdu, kur<PERSON> nosūt<PERSON><PERSON> atbildi, lai atbilde būtu d<PERSON>."}, "PROFESSIONAL": {"TITLE": "Profesion<PERSON><PERSON>", "SUBTITLE": "Izmantot tikai konfigurēto uz<PERSON>a nosaukumu kā sūtītāja vārdu e-pasta galvenē."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>mu", "PLACEHOLDER": "Ievadiet uzņēmuma no<PERSON>ukumu", "SAVE_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ENABLE_HMAC": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "AVATAR_DELETE_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Vai vēlaties izdzēst ", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON>, uzrakstiet {inboxName} lai apstip<PERSON>tu", "YES": "Jā, Dzēst ", "NO": "Nē, Paturēt "}, "API": {"SUCCESS_MESSAGE": "Iesūtne veiksmīgi izdzēsta", "ERROR_MESSAGE": "Nevarēja izdzēst iesūtni. Lū<PERSON><PERSON>, vēlāk pamēģiniet vēlreiz.", "AVATAR_SUCCESS_MESSAGE": "Iesūtnes avatārs ir veiksmīgi izdz<PERSON>", "AVATAR_ERROR_MESSAGE": "Nevarēja izdzēst iesūtnes avatāru. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz."}}, "TABS": {"SETTINGS": "Iestatījumi", "COLLABORATORS": "Līdzstrādnieki", "CONFIGURATION": "Konfigurācija", "CAMPAIGN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PRE_CHAT_FORM": "Pirms-T<PERSON><PERSON><PERSON><PERSON><PERSON> V<PERSON>dl<PERSON>", "BUSINESS_HOURS": "Darba Laiks", "WIDGET_BUILDER": "Logr<PERSON><PERSON>", "BOT_CONFIGURATION": "Robota Konfigurā<PERSON>", "CSAT": "CSAT"}, "SETTINGS": "Iestatījumi", "FEATURES": {"LABEL": "Īpašī<PERSON>", "DISPLAY_FILE_PICKER": "Logr<PERSON><PERSON><PERSON> parādīt failu atlasītāju", "DISPLAY_EMOJI_PICKER": "Logrīk<PERSON> parādīt emocijzīmju atlasītāju", "ALLOW_END_CONVERSATION": "<PERSON>ļaut lieto<PERSON>em beigt sarunu no logrīka", "USE_INBOX_AVATAR_FOR_BOT": "Izmantot iesūtnes nosaukumu un avataru priekš robota"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON>", "MESSENGER_SUB_HEAD": "Ievietojiet šo pogu savā body tagā", "INBOX_AGENTS": "Aģenti", "INBOX_AGENTS_SUB_TEXT": "Pievienot vai noņemt aģentus no šīs ies<PERSON>nes", "AGENT_ASSIGNMENT": "<PERSON><PERSON><PERSON> Piešķiršana", "AGENT_ASSIGNMENT_SUB_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sarunas piešķiršanas iestatījumus", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Iespējot e-pasta ieg<PERSON><PERSON> lod<PERSON>", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Iespējot vai atspējot e-pasta ieg<PERSON><PERSON><PERSON> lo<PERSON> jaunai sarunai", "AUTO_ASSIGNMENT": "<PERSON>es<PERSON><PERSON><PERSON><PERSON> automātis<PERSON> piešķiršanu", "SENDER_NAME_SECTION": "Iespējot Aģenta Vārdu E-pastā", "SENDER_NAME_SECTION_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Atspējot aģenta vārda rādīšanu e-pastā. Ja tas ir atspējots, tiks rādīts uzņēmuma nosaukums", "ENABLE_CONTINUITY_VIA_EMAIL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>as <PERSON>, i<PERSON><PERSON>jot e-pastu", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "<PERSON><PERSON>as turpināsies pa e-pastu, ja saziņas e-pasta adrese ir pieejama.", "LOCK_TO_SINGLE_CONVERSATION": "Pieturēties pie vienas sarunas", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Iespējot vai atspējot vairākas sarunas vienai un tai pašai kontaktpersonai šajā iesūtnē", "INBOX_UPDATE_TITLE": "Iesū<PERSON>nes Iestatījumi", "INBOX_UPDATE_SUB_TEXT": "Atjaunināt <PERSON> i<PERSON>ūtnes iestatījumus", "AUTO_ASSIGNMENT_SUB_TEXT": "Iespējot vai atspējot jaunu sarunu automātisku piešķiršanu šai iesūtnei pievienotajiem aģentiem.", "HMAC_VERIFICATION": "Lietotāja Identitātes Apstiprināšana", "HMAC_DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo at<PERSON>l<PERSON>gu, varat ģenerēt slepeno marķieri, ko var izman<PERSON>, lai pārb<PERSON><PERSON><PERSON> lietotāju identitāti.", "HMAC_LINK_TO_DOCS": "Papildu informācija ir pieejama šeit.", "HMAC_MANDATORY_VERIFICATION": "Īstenot Lietotāja Identitātes Validāciju", "HMAC_MANDATORY_DESCRIPTION": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kurus nevar <PERSON>, t<PERSON><PERSON>.", "INBOX_IDENTIFIER": "Iesūtnes identifikators", "INBOX_IDENTIFIER_SUB_TEXT": "Izmantojiet šeit uzrādīto `inbox_identifier` token, lai autentificētu savus API klientus.", "FORWARD_EMAIL_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz E-pastu", "FORWARD_EMAIL_SUB_TEXT": "Sākt pā<PERSON><PERSON>t<PERSON>t savus e-pasta ziņojumus uz tālāk norādīto e-pasta adresi.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Atļaut ziņ<PERSON><PERSON><PERSON> pēc sa<PERSON> p<PERSON>", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Ļaut galalietotā<PERSON>em sūt<PERSON>t ziņ<PERSON> pat pēc sarunas at<PERSON>.", "WHATSAPP_SECTION_SUBHEADER": "Šī API atslēga tiek izmantota integrācijai ar WhatsApp API.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Ievadiet jauno API atslēgu, kas tiks izmantota integrācijai ar WhatsApp API.", "WHATSAPP_SECTION_TITLE": "API atslēga", "WHATSAPP_SECTION_UPDATE_TITLE": "Atjaunināt API atslēgu", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Ievadiet šeit jauno API atslēgu", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verifikācijas Token", "WHATSAPP_WEBHOOK_SUBHEADER": "<PERSON><PERSON> marķieris tiek <PERSON>, lai <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> webhook endpoint autentiskumu.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Atjaunināt pirms-t<PERSON><PERSON><PERSON><PERSON><PERSON> veidlapas iestatījumus"}, "HELP_CENTER": {"LABEL": "Palīdzības centrs", "PLACEHOLDER": "Izvēlēties Palīdzības Centru", "SELECT_PLACEHOLDER": "Izvēlēties Palīdzības Centru", "REMOVE": "Noņemt Palīdzības Centru", "SUB_TEXT": "<PERSON><PERSON><PERSON>dzības Centru ar i<PERSON>ni"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Automātiskās piešķiršanas ierobežojums", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> ir <PERSON> par 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Ierobežo maks<PERSON> sa<PERSON>u skaitu no šī<PERSON>, ko var automātiski piešķirt aģentam"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Atkārtoti autorizēties", "SUBTITLE": "Jūsu Facebook savienojuma derīguma termiņ<PERSON> ir beidzie<PERSON>. <PERSON><PERSON><PERSON><PERSON>, atkārtoti pievienojiet savu <PERSON> lapu, lai turpin<PERSON><PERSON> paka<PERSON>", "MESSAGE_SUCCESS": "Atkārtota savienojuma izveide ir veiksmīga", "MESSAGE_ERROR": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON>"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pirms tērzēšanas ve<PERSON>auj iegūt informāciju par lietot<PERSON>, pirms viņi sāk ar <PERSON>.", "SET_FIELDS": "Pirms tērzē<PERSON> ve<PERSON> lauki", "SET_FIELDS_HEADER": {"FIELDS": "<PERSON><PERSON>", "LABEL": "Etiķete", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON>", "KEY": "Atslēga", "TYPE": "Tips", "REQUIRED": "Nepieciešams"}, "ENABLE": {"LABEL": "Iespējot pirms tērzē<PERSON> veid<PERSON>u", "OPTIONS": {"ENABLED": "Jā", "DISABLED": "Nē"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pirms tērzēšanas ziņojums", "PLACEHOLDER": "<PERSON><PERSON> būs <PERSON>, kop<PERSON> ar ve<PERSON>"}, "REQUIRE_EMAIL": {"LABEL": "Apmeklētājiem pirms tērzēšanas ir jānorāda savs vārds un e-pasta adrese"}}, "CSAT": {"TITLE": "Iespējot CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "satur", "DOES_NOT_CONTAINS": "nesatur"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Iestatīt savu pieej<PERSON>bu", "SUBTITLE": "Iestatīt savu pieejamību tiešraides tērzēša<PERSON> logrī<PERSON>", "WEEKLY_TITLE": "<PERSON><PERSON><PERSON><PERSON>t savu darba laiku", "TIMEZONE_LABEL": "Izvēlie<PERSON> laika j<PERSON>lu", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darba laika iestat<PERSON>", "TOGGLE_AVAILABILITY": "Iespējot biznesa pieejamību šai iesūtnei", "UNAVAILABLE_MESSAGE_LABEL": "Ziņojums nav pieejams priekš apmeklētājiem", "TOGGLE_HELP": "Iespējo<PERSON><PERSON> u<PERSON><PERSON>, tie<PERSON><PERSON><PERSON> tērzēšanas logrīkā tiks rādīts darba laiks, pat ja visi aģenti būs bezsaistē. Ārpus darba laika apmeklētājus var brīdināt ar ziņojumu un pirms tērzēšanas veidlapu.", "DAY": {"ENABLE": "Iespējot <PERSON>nai", "UNAVAILABLE": "Nav pieejams", "HOURS": "darba laiks", "VALIDATION_ERROR": "<PERSON><PERSON><PERSON><PERSON> laikam jāb<PERSON>t pirms slēgšanas laika.", "CHOOSE": "Izvēlēties"}, "ALL_DAY": "<PERSON><PERSON><PERSON> dienu"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Iestatīt IMAP informāciju", "NOTE_TEXT": "<PERSON> i<PERSON>, <PERSON><PERSON><PERSON><PERSON>, nokonfigurējiet IMAP.", "UPDATE": "Atjaunināt IMAP iestatījumus", "TOGGLE_AVAILABILITY": "Iespējot IMAP konfigurāciju šai iesūtnei", "TOGGLE_HELP": "Iespējojot IMAP, lietot<PERSON><PERSON><PERSON> varēs saņ<PERSON>t e-pastu", "EDIT": {"SUCCESS_MESSAGE": "IMAP iestatījumi ir veik<PERSON> at<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON>t IMAP iestatījumus"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON> (piemēram: imap.gmail.com)"}, "PORT": {"LABEL": "Ports", "PLACE_HOLDER": "Ports"}, "LOGIN": {"LABEL": "Lietotājvārds", "PLACE_HOLDER": "Lietotājvārds"}, "PASSWORD": {"LABEL": "Parole", "PLACE_HOLDER": "Parole"}, "ENABLE_SSL": "Iespējot SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Atkārtoti autorizēt savu MICROSOFT kontu"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Iestatīt SMTP informāciju", "UPDATE": "Atjaunināt SMTP iestatījumus", "TOGGLE_AVAILABILITY": "Iespējot SMTP konfigurāciju šai iesūtnei", "TOGGLE_HELP": "Iespējojot SMTP lietotājs varēs nosūtīt e-pastu", "EDIT": {"SUCCESS_MESSAGE": "SMTP iestatījumi ir veiksm<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON> SMTP iestatījumus"}, "ADDRESS": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "Adrese (piemēram: smtp.gmail.com)"}, "PORT": {"LABEL": "Ports", "PLACE_HOLDER": "Ports"}, "LOGIN": {"LABEL": "Lietotājvārds", "PLACE_HOLDER": "Lietotājvārds"}, "PASSWORD": {"LABEL": "Parole", "PLACE_HOLDER": "Parole"}, "DOMAIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "ENCRYPTION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Režīms", "AUTH_MECHANISM": "Autentifikācija"}, "NOTE": "Piezīme: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Tīmekļa Viet<PERSON> Avatā<PERSON>", "DELETE": {"API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> ir ve<PERSON> i<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON>"}}}, "WEBSITE_NAME": {"LABEL": "Tīmekļa Vietnes No<PERSON>ukums", "PLACE_HOLDER": "Ievadiet Jū<PERSON> tīmekļa vietnes nosaukumu (piemēram: Acme Inc)", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu tīmekļa vietnes nosaukumu"}, "WELCOME_HEADING": {"LABEL": "<PERSON><PERSON><PERSON> <PERSON>", "PLACE_HOLDER": "Sveicināti!"}, "WELCOME_TAGLINE": {"LABEL": "Laipni lūd<PERSON>", "PLACE_HOLDER": "Sazināties ar mums ir vien<PERSON><PERSON><PERSON><PERSON>. Vaicājiet mums j<PERSON><PERSON>, vai dalieties ar savām atsauksmēm."}, "REPLY_TIME": {"LABEL": "Atbildes Laiks", "IN_A_FEW_MINUTES": "<PERSON><PERSON><PERSON><PERSON>", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "IN_A_DAY": "<PERSON><PERSON>"}, "WIDGET_COLOR_LABEL": "Logrīka <PERSON>", "WIDGET_BUBBLE_POSITION_LABEL": "Logrīka Burbuļa Pozīcija", "WIDGET_BUBBLE_TYPE_LABEL": "Logrīka <PERSON> Tip<PERSON>", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar mums", "LABEL": "Logrīka Burbuļa Palaidē<PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar mums"}, "UPDATE": {"BUTTON_TEXT": "Atjaunināt Logrīka Iestatījumus", "API": {"SUCCESS_MESSAGE": "Logrīka iestatīju<PERSON> ir veik<PERSON>gi at<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON>r<PERSON> i<PERSON>ī<PERSON>"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Priekšskatījums", "SCRIPT": "<PERSON><PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "<PERSON> kreisi", "RIGHT": "Pa labi"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standarta", "EXPANDED_BUBBLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "WIDGET_SCREEN": {"DEFAULT": "Noklusējums", "CHAT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Parasti atbild dažu minūšu laikā", "IN_A_FEW_HOURS": "<PERSON>st<PERSON> at<PERSON> da<PERSON>u stundu la<PERSON>", "IN_A_DAY": "Parasti atbild vienas dienas laik<PERSON>"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "CHAT_INPUT_PLACEHOLDER": "Rakstiet savu ziņojumu"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "<PERSON><PERSON><PERSON>", "OFFLINE": "Š<PERSON><PERSON><PERSON><PERSON> neesam uz vietas"}, "USER_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AGENT_MESSAGE": "Labdien"}, "BRANDING_TEXT": "Darbinā<PERSON> ar <PERSON>", "SCRIPT_SETTINGS": "\nwindow.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Citi Pakalpojuma Sniedzēji"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Tīmekļa vietne", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "E-pasts", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Kanāls", "INSTAGRAM": "Instagram", "VOICE": "Voice"}}}