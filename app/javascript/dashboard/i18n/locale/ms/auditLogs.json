{"AUDIT_LOGS": {"HEADER": "<PERSON><PERSON>", "HEADER_BTN_TXT": "Add Audit Logs", "LOADING": "Fetching <PERSON>t <PERSON>gs", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "There are no items matching this query", "SIDEBAR_TXT": "<p><b><PERSON>t Logs</b> </p><p> Audit Logs are trails for events and actions in a Chatwoot System. </p>", "LIST": {"404": "There are no Audit Logs available in this account.", "TITLE": "Manage Audit Logs", "DESC": "Audit Logs are trails for events and actions in a Chatwoot System.", "TABLE_HEADER": {"ACTIVITY": "User", "TIME": "Action", "IP_ADDRESS": "IP Address"}}, "API": {"SUCCESS_MESSAGE": "AuditLogs retrieved successfully", "ERROR_MESSAGE": "Ma<PERSON>ah untuk hubungi Woot Server, <PERSON>la cuba sebentar lagi"}, "DEFAULT_USER": "System", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{<PERSON><PERSON><PERSON>} invited {invitee} to the account as an {role}", "EDIT": {"SELF": "{agent<PERSON><PERSON>} changed their {attributes} to {values}", "OTHER": "{agent<PERSON><PERSON>} changed {attributes} of {user} to {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{<PERSON><PERSON><PERSON>} signed in", "SIGN_OUT": "{<PERSON><PERSON><PERSON>} signed out"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}, "CONVERSATION": {"DELETE": "{agent<PERSON><PERSON>} deleted conversation #{id}"}}}